"""
测试修复后的终止条件
验证PPO现在能否删除更多节点
"""

import numpy as np
import networkx as nx
from epidemic_environment import EpidemicEnvironment
from final_optimized_train import create_synthetic_networks

def test_new_termination():
    """测试新的终止条件"""
    print("=== 测试新的终止条件 ===")
    
    # 创建网络
    disease_graph, rumor_graph = create_synthetic_networks(200, 'WS', k=8, p=0.3)
    
    # 创建环境
    env = EpidemicEnvironment(
        disease_graph=disease_graph,
        rumor_graph=rumor_graph,
        max_steps=60,
        beta_d=0.2, gamma_d=0.1, beta_r=0.3, mu_r=0.15,
        lambda_dr=0.3, lambda_rd=0.2,
        alpha=0.25, beta=0.75, gamma=0, delta=0.01
    )
    
    print(f"网络规模: {len(disease_graph.nodes())} 节点")
    print(f"最小删除数: {max(1, int(0.05 * len(disease_graph.nodes())))} 个节点")
    
    # 重置环境
    obs = env.reset()
    print(f"初始感染者数量: {np.sum(env.disease_states == 1)}")
    
    # 模拟执行
    step_count = 0
    total_reward = 0
    
    for step in range(60):
        current_infected = np.sum(env.disease_states == 1)
        
        print(f"\n步骤 {step+1}:")
        print(f"  感染者: {current_infected}")
        print(f"  已删除: {len(env.deleted_nodes)} 个节点")
        
        # 随机选择动作
        action_mask = env.get_action_mask()
        available_actions = np.where(action_mask)[0]
        
        if len(available_actions) == 0:
            print("  没有可用动作，结束")
            break
            
        action = np.random.choice(available_actions)
        
        # 执行动作
        next_obs, reward, done, info = env.step(action)
        total_reward += reward
        step_count += 1
        
        print(f"  删除节点: {action}")
        print(f"  奖励: {reward:.4f}")
        print(f"  感染数变化: {current_infected} -> {info['infected_count']}")
        print(f"  完成标志: {done}")
        
        if done:
            print(f"  终止原因分析:")
            print(f"    - 感染数: {info['infected_count']}")
            print(f"    - 删除数: {len(env.deleted_nodes)}")
            print(f"    - 最小删除数: {max(1, int(0.05 * 200))}")
            print(f"    - 当前步数: {env.current_step}")
            print(f"    - 最大步数: {env.max_steps}")
            break
    
    print(f"\n总结:")
    print(f"  执行步数: {step_count}")
    print(f"  累积奖励: {total_reward:.4f}")
    print(f"  最终感染数: {np.sum(env.disease_states == 1)}")
    print(f"  删除节点数: {len(env.deleted_nodes)}")

def test_different_scenarios():
    """测试不同场景下的终止条件"""
    print("\n=== 测试不同场景 ===")
    
    scenarios = [
        {"n_nodes": 50, "min_del_ratio": 0.05, "name": "小网络"},
        {"n_nodes": 100, "min_del_ratio": 0.05, "name": "中网络"},
        {"n_nodes": 200, "min_del_ratio": 0.05, "name": "大网络"}
    ]
    
    for scenario in scenarios:
        print(f"\n{scenario['name']} ({scenario['n_nodes']} 节点):")
        
        disease_graph, rumor_graph = create_synthetic_networks(
            scenario['n_nodes'], 'WS', k=6, p=0.3
        )
        
        env = EpidemicEnvironment(
            disease_graph=disease_graph,
            rumor_graph=rumor_graph,
            max_steps=30,
            beta_d=0.2, gamma_d=0.1, beta_r=0.3, mu_r=0.15,
            lambda_dr=0.3, lambda_rd=0.2
        )
        
        min_deletions = max(1, int(scenario['min_del_ratio'] * scenario['n_nodes']))
        print(f"  最小删除数: {min_deletions}")
        
        env.reset()
        
        # 快速测试：删除到最小数量
        for i in range(min_deletions + 2):
            action_mask = env.get_action_mask()
            available_actions = np.where(action_mask)[0]
            
            if len(available_actions) == 0:
                break
                
            action = np.random.choice(available_actions)
            next_obs, reward, done, info = env.step(action)
            
            print(f"    删除{i+1}: 感染{info['infected_count']}, 完成{done}")
            
            if done:
                break

def main():
    """主测试函数"""
    print("测试修复后的终止条件...")
    
    # 测试新的终止条件
    test_new_termination()
    
    # 测试不同场景
    test_different_scenarios()
    
    print("\n修复总结:")
    print("✅ 终止条件改进：需要删除至少5%的节点才能在感染清零时终止")
    print("✅ 给PPO更多学习机会")
    print("✅ 避免过早终止")

if __name__ == "__main__":
    main()
