"""
诊断PPO性能问题
分析为什么PPO只删除1个节点就结束
"""

import numpy as np
import networkx as nx
from epidemic_environment import EpidemicEnvironment
from final_optimized_train import create_synthetic_networks

def diagnose_environment():
    """诊断环境行为"""
    print("=== 环境行为诊断 ===")
    
    # 创建与训练相同的网络
    disease_graph, rumor_graph = create_synthetic_networks(200, 'WS', k=8, p=0.3)
    
    # 创建环境
    env = EpidemicEnvironment(
        disease_graph=disease_graph,
        rumor_graph=rumor_graph,
        max_steps=60,
        beta_d=0.4, gamma_d=0.05, beta_r=0.5, mu_r=0.1,
        lambda_dr=0.6, lambda_rd=0.4,
        alpha=0.25, beta=0.75, gamma=0, delta=0.01
    )
    
    print(f"网络规模: {len(disease_graph.nodes())} 节点")
    
    # 重置环境
    obs = env.reset()
    print(f"初始感染者数量: {np.sum(env.disease_states == 1)}")
    print(f"初始谣言传播者数量: {np.sum(env.rumor_states == 1)}")
    
    # 模拟多步执行
    step_count = 0
    total_reward = 0
    
    for step in range(20):  # 最多20步
        # 获取当前状态
        current_infected = np.sum(env.disease_states == 1)
        current_rumor = np.sum(env.rumor_states == 1)
        
        print(f"\n步骤 {step+1}:")
        print(f"  感染者: {current_infected}, 谣言者: {current_rumor}")
        
        # 随机选择动作
        action_mask = env.get_action_mask()
        available_actions = np.where(action_mask)[0]
        
        if len(available_actions) == 0:
            print("  没有可用动作，结束")
            break
            
        action = np.random.choice(available_actions)
        
        # 执行动作
        next_obs, reward, done, info = env.step(action)
        total_reward += reward
        step_count += 1
        
        print(f"  删除节点: {action}")
        print(f"  奖励: {reward:.2f}")
        print(f"  感染数变化: {current_infected} -> {info['infected_count']}")
        print(f"  完成标志: {done}")
        
        if done:
            print(f"  终止原因: 感染数={info['infected_count']}, 步数={env.current_step}/{env.max_steps}")
            break
    
    print(f"\n总结:")
    print(f"  执行步数: {step_count}")
    print(f"  累积奖励: {total_reward:.2f}")
    print(f"  最终感染数: {np.sum(env.disease_states == 1)}")
    print(f"  删除节点数: {len(env.deleted_nodes)}")

def test_reward_sensitivity():
    """测试奖励函数敏感性"""
    print("\n=== 奖励函数敏感性测试 ===")
    
    # 创建简单环境
    disease_graph, rumor_graph = create_synthetic_networks(50, 'WS', k=6, p=0.3)
    
    env = EpidemicEnvironment(
        disease_graph=disease_graph,
        rumor_graph=rumor_graph,
        max_steps=20,
        beta_d=0.4, gamma_d=0.05, beta_r=0.5, mu_r=0.1,
        lambda_dr=0.6, lambda_rd=0.4,
        alpha=0.25, beta=0.75, gamma=0, delta=0.01
    )
    
    # 测试不同感染数的奖励
    env.reset()
    
    test_cases = [
        (10, "高感染"),
        (5, "中感染"), 
        (2, "低感染"),
        (0, "无感染")
    ]
    
    for infected_count, description in test_cases:
        # 手动设置感染数
        env.disease_states[:] = 2  # 全部恢复
        env.disease_states[:infected_count] = 1  # 设置感染数
        
        reward = env._calculate_reward()
        print(f"  {description} ({infected_count}个): 奖励 = {reward}")

def test_early_termination():
    """测试早期终止条件"""
    print("\n=== 早期终止条件测试 ===")
    
    disease_graph, rumor_graph = create_synthetic_networks(50, 'WS', k=6, p=0.3)
    
    env = EpidemicEnvironment(
        disease_graph=disease_graph,
        rumor_graph=rumor_graph,
        max_steps=20
    )
    
    env.reset()
    
    # 测试不同情况的终止条件
    test_cases = [
        (5, "有感染者"),
        (1, "少量感染者"),
        (0, "无感染者")
    ]
    
    for infected_count, description in test_cases:
        env.disease_states[:] = 2  # 全部恢复
        env.disease_states[:infected_count] = 1  # 设置感染数
        
        early_term = env._check_early_termination()
        print(f"  {description} ({infected_count}个): 早期终止 = {early_term}")

def main():
    """主诊断函数"""
    print("开始PPO性能诊断...")
    
    # 诊断环境行为
    diagnose_environment()
    
    # 测试奖励敏感性
    test_reward_sensitivity()
    
    # 测试早期终止
    test_early_termination()
    
    print("\n诊断完成！")
    print("\n可能的问题:")
    print("1. 感染传播太快，很快就清零导致早期终止")
    print("2. 奖励函数梯度太大，PPO难以学习")
    print("3. max_steps设置合理，但环境自然终止太早")
    print("4. 需要调整传播参数或增加初始感染数")

if __name__ == "__main__":
    main()
