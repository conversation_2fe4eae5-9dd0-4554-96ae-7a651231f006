"""
测试1000节点网络的PPO收敛性和与规则方法的对比
包含多种规则方法：Random, Degree, Betweenness, K-shell, Closeness, PageRank
"""

import numpy as np
import networkx as nx
import torch
import copy
from rule_delete import DegreeDeleter, RandomDeleter, KShellDeleter, BetweennessDeleter
from epidemic_environment import EpidemicEnvironment
from ppo_agent import PPOAgent
from dual_gnn_encoder import GraphDataProcessor
from final_optimized_train import create_synthetic_networks
import time

class ClosenessDeleter:
    """基于接近中心性的删除策略"""
    def __init__(self, disease_graph, rumor_graph, max_steps):
        self.disease_graph = copy.deepcopy(disease_graph)
        self.rumor_graph = copy.deepcopy(rumor_graph)
        self.max_steps = max_steps
        self.deleted_nodes = set()
        self.ever_infected_nodes = set()
        
        # 初始化感染状态
        self._initialize_infection()
        
    def _initialize_infection(self):
        """初始化感染状态"""
        nodes = list(self.disease_graph.nodes())
        initial_infected = np.random.choice(nodes, size=min(10, len(nodes)), replace=False)
        
        for node in self.disease_graph.nodes():
            if node in initial_infected:
                self.disease_graph.nodes[node]['state'] = 'I'
                self.ever_infected_nodes.add(node)
            else:
                self.disease_graph.nodes[node]['state'] = 'S'
    
    def select_node_to_delete(self):
        """选择要删除的节点 - 基于接近中心性"""
        available_nodes = [n for n in self.disease_graph.nodes() if n not in self.deleted_nodes]
        if not available_nodes:
            return None
            
        # 计算接近中心性
        try:
            closeness = nx.closeness_centrality(self.disease_graph.subgraph(available_nodes))
            if closeness:
                return max(closeness.keys(), key=lambda x: closeness[x])
        except:
            pass
        
        return np.random.choice(available_nodes) if available_nodes else None
    
    def delete_node(self, node):
        """删除节点"""
        if node in self.disease_graph.nodes():
            self.disease_graph.remove_node(node)
            self.deleted_nodes.add(node)
        if node in self.rumor_graph.nodes():
            self.rumor_graph.remove_node(node)
    
    def propagate_one_step(self):
        """传播一步"""
        # 简化的传播模型
        current_infected = [n for n in self.disease_graph.nodes() 
                          if self.disease_graph.nodes[n].get('state') == 'I']
        
        new_infected = []
        for infected_node in current_infected:
            neighbors = list(self.disease_graph.neighbors(infected_node))
            for neighbor in neighbors:
                if (self.disease_graph.nodes[neighbor].get('state') == 'S' and 
                    np.random.random() < 0.2):  # 感染概率
                    new_infected.append(neighbor)
        
        # 更新状态
        for node in new_infected:
            self.disease_graph.nodes[node]['state'] = 'I'
            self.ever_infected_nodes.add(node)
        
        # 康复
        for node in current_infected:
            if np.random.random() < 0.1:  # 康复概率
                self.disease_graph.nodes[node]['state'] = 'R'
    
    def run_simulation(self):
        """运行完整模拟"""
        history = []
        
        for step in range(self.max_steps):
            # 传播
            self.propagate_one_step()
            
            # 记录当前感染数
            current_infected = len([n for n in self.disease_graph.nodes() 
                                  if self.disease_graph.nodes[n].get('state') == 'I'])
            history.append(current_infected)
            
            # 如果没有感染者，结束
            if current_infected == 0:
                break
            
            # 删除节点
            node_to_delete = self.select_node_to_delete()
            if node_to_delete is not None:
                self.delete_node(node_to_delete)
            
            # 如果图为空，结束
            if len(self.disease_graph.nodes()) == 0:
                break
        
        return history

class PageRankDeleter:
    """基于PageRank的删除策略"""
    def __init__(self, disease_graph, rumor_graph, max_steps):
        self.disease_graph = copy.deepcopy(disease_graph)
        self.rumor_graph = copy.deepcopy(rumor_graph)
        self.max_steps = max_steps
        self.deleted_nodes = set()
        self.ever_infected_nodes = set()
        
        # 初始化感染状态
        self._initialize_infection()
        
    def _initialize_infection(self):
        """初始化感染状态"""
        nodes = list(self.disease_graph.nodes())
        initial_infected = np.random.choice(nodes, size=min(10, len(nodes)), replace=False)
        
        for node in self.disease_graph.nodes():
            if node in initial_infected:
                self.disease_graph.nodes[node]['state'] = 'I'
                self.ever_infected_nodes.add(node)
            else:
                self.disease_graph.nodes[node]['state'] = 'S'
    
    def select_node_to_delete(self):
        """选择要删除的节点 - 基于PageRank"""
        available_nodes = [n for n in self.disease_graph.nodes() if n not in self.deleted_nodes]
        if not available_nodes:
            return None
            
        # 计算PageRank
        try:
            pagerank = nx.pagerank(self.disease_graph.subgraph(available_nodes))
            if pagerank:
                return max(pagerank.keys(), key=lambda x: pagerank[x])
        except:
            pass
        
        return np.random.choice(available_nodes) if available_nodes else None
    
    def delete_node(self, node):
        """删除节点"""
        if node in self.disease_graph.nodes():
            self.disease_graph.remove_node(node)
            self.deleted_nodes.add(node)
        if node in self.rumor_graph.nodes():
            self.rumor_graph.remove_node(node)
    
    def propagate_one_step(self):
        """传播一步"""
        # 简化的传播模型
        current_infected = [n for n in self.disease_graph.nodes() 
                          if self.disease_graph.nodes[n].get('state') == 'I']
        
        new_infected = []
        for infected_node in current_infected:
            neighbors = list(self.disease_graph.neighbors(infected_node))
            for neighbor in neighbors:
                if (self.disease_graph.nodes[neighbor].get('state') == 'S' and 
                    np.random.random() < 0.2):  # 感染概率
                    new_infected.append(neighbor)
        
        # 更新状态
        for node in new_infected:
            self.disease_graph.nodes[node]['state'] = 'I'
            self.ever_infected_nodes.add(node)
        
        # 康复
        for node in current_infected:
            if np.random.random() < 0.1:  # 康复概率
                self.disease_graph.nodes[node]['state'] = 'R'
    
    def run_simulation(self):
        """运行完整模拟"""
        history = []
        
        for step in range(self.max_steps):
            # 传播
            self.propagate_one_step()
            
            # 记录当前感染数
            current_infected = len([n for n in self.disease_graph.nodes() 
                                  if self.disease_graph.nodes[n].get('state') == 'I'])
            history.append(current_infected)
            
            # 如果没有感染者，结束
            if current_infected == 0:
                break
            
            # 删除节点
            node_to_delete = self.select_node_to_delete()
            if node_to_delete is not None:
                self.delete_node(node_to_delete)
            
            # 如果图为空，结束
            if len(self.disease_graph.nodes()) == 0:
                break
        
        return history

def test_1000_nodes_convergence():
    """测试1000节点网络的收敛性"""
    print("=== 测试1000节点网络的PPO收敛性 ===")
    
    # 创建1000节点网络
    print("创建1000节点网络...")
    disease_graph, rumor_graph = create_synthetic_networks(1000, 'WS', k=8, p=0.3)
    
    print(f"网络规模: {len(disease_graph.nodes())} 节点")
    print(f"疾病网络边数: {len(disease_graph.edges())}")
    print(f"谣言网络边数: {len(rumor_graph.edges())}")
    
    # 测试规则方法
    methods = {
        'Random': RandomDeleter,
        'Degree': DegreeDeleter,
        'Betweenness': BetweennessDeleter,
        'K-shell': KShellDeleter,
        'Closeness': ClosenessDeleter,
        'PageRank': PageRankDeleter
    }
    
    fixed_deletions = 50  # 固定删除50个节点
    num_runs = 3
    
    results = {}
    
    print(f"\n=== 规则方法对比（固定删除{fixed_deletions}个节点）===")
    
    for method_name, method_class in methods.items():
        print(f"\n测试{method_name}方法...")
        method_results = []
        
        for run in range(num_runs):
            start_time = time.time()
            
            # 创建新的网络副本
            test_disease = copy.deepcopy(disease_graph)
            test_rumor = copy.deepcopy(rumor_graph)
            
            try:
                deleter = method_class(test_disease, test_rumor, max_steps=fixed_deletions)
                history = deleter.run_simulation()

                if history:
                    # 对于没有ever_infected_nodes属性的类，估算累积感染数
                    if hasattr(deleter, 'ever_infected_nodes'):
                        cumulative_infection = len(deleter.ever_infected_nodes)
                    else:
                        # 估算：假设峰值感染数的1.2倍作为累积感染数
                        cumulative_infection = int(max(history) * 1.2)

                    result = {
                        'peak_infection': max(history),
                        'final_infection': history[-1],
                        'duration': len(history),
                        'cumulative_infection': cumulative_infection,
                        'deleted_count': fixed_deletions  # 固定删除数
                    }
                    method_results.append(result)
                    
                    elapsed = time.time() - start_time
                    print(f"  运行{run+1}: 峰值{result['peak_infection']}, 最终{result['final_infection']}, "
                          f"累积{result['cumulative_infection']}, 用时{elapsed:.1f}s")
                
            except Exception as e:
                print(f"  运行{run+1}失败: {e}")
        
        if method_results:
            # 计算平均值
            avg_result = {
                'peak_infection': np.mean([r['peak_infection'] for r in method_results]),
                'final_infection': np.mean([r['final_infection'] for r in method_results]),
                'duration': np.mean([r['duration'] for r in method_results]),
                'cumulative_infection': np.mean([r['cumulative_infection'] for r in method_results]),
                'deleted_count': np.mean([r['deleted_count'] for r in method_results])
            }
            results[method_name] = avg_result
            
            print(f"  {method_name}平均结果:")
            print(f"    峰值感染数: {avg_result['peak_infection']:.1f}")
            print(f"    最终感染数: {avg_result['final_infection']:.1f}")
            print(f"    累积感染数: {avg_result['cumulative_infection']:.1f}")
            print(f"    删除节点数: {avg_result['deleted_count']:.1f}")
    
    # 测试PPO收敛性
    print(f"\n=== 测试PPO在1000节点网络上的收敛性 ===")
    
    try:
        # 创建环境
        env = EpidemicEnvironment(
            disease_graph=disease_graph,
            rumor_graph=rumor_graph,
            max_steps=100,
            beta_d=0.2, gamma_d=0.1, beta_r=0.3, mu_r=0.15,
            lambda_dr=0.3, lambda_rd=0.2,
            alpha=0.25, beta=0.75, gamma=0, delta=0.01
        )
        
        # 创建PPO智能体
        agent = PPOAgent(
            node_feature_dim=env.node_feature_dim,
            hidden_dim=128,
            num_gnn_layers=4
        )
        
        # 尝试加载已训练的模型
        model_path = "final_results/final_ppo_training_20250626_051012/best_model.pth"
        
        try:
            checkpoint = torch.load(model_path, map_location='cpu')
            agent.actor.load_state_dict(checkpoint['actor_state_dict'])
            agent.critic.load_state_dict(checkpoint['critic_state_dict'])
            agent.actor.eval()
            agent.critic.eval()
            print("成功加载预训练PPO模型")
            
            # 测试PPO性能
            ppo_results = []
            
            for run in range(3):
                print(f"\nPPO运行{run+1}:")
                start_time = time.time()
                
                obs = env.reset()
                ever_infected = set()
                infection_history = []
                
                # 记录初始感染者
                for i, state in enumerate(env.disease_states):
                    if state == 1:
                        ever_infected.add(i)
                
                step_count = 0
                done = False
                
                while not done and step_count < 100:
                    # 记录当前感染者
                    current_infected = set()
                    for i, state in enumerate(env.disease_states):
                        if state == 1:
                            current_infected.add(i)
                            ever_infected.add(i)
                    
                    infection_history.append(len(current_infected))
                    
                    # PPO选择动作
                    action_mask = env.get_action_mask()
                    
                    processor = GraphDataProcessor()
                    disease_edge_index, rumor_edge_index, _ = processor.networkx_to_pyg(
                        env.disease_graph, env.rumor_graph, obs
                    )
                    
                    env_state = {
                        'node_features': obs,
                        'disease_edge_index': disease_edge_index,
                        'rumor_edge_index': rumor_edge_index,
                        'action_mask': action_mask
                    }
                    
                    try:
                        action, log_prob, value = agent.select_action(env_state, deterministic=True)
                    except Exception as e:
                        # 如果PPO失败，随机选择
                        available_actions = np.where(action_mask)[0]
                        if len(available_actions) > 0:
                            action = np.random.choice(available_actions)
                        else:
                            break
                    
                    obs, reward, done, info = env.step(action)
                    step_count += 1
                    
                    if step_count % 20 == 0:
                        print(f"    步骤{step_count}: 当前感染{len(current_infected)}, "
                              f"累积感染{len(ever_infected)}, 删除{len(env.deleted_nodes)}")
                
                elapsed = time.time() - start_time
                
                if infection_history:
                    result = {
                        'peak_infection': max(infection_history),
                        'final_infection': infection_history[-1],
                        'duration': len(infection_history),
                        'cumulative_infection': len(ever_infected),
                        'deleted_count': len(env.deleted_nodes)
                    }
                    ppo_results.append(result)
                    
                    print(f"  PPO结果: 峰值{result['peak_infection']}, 最终{result['final_infection']}, "
                          f"累积{result['cumulative_infection']}, 删除{result['deleted_count']}, 用时{elapsed:.1f}s")
            
            if ppo_results:
                avg_ppo = {
                    'peak_infection': np.mean([r['peak_infection'] for r in ppo_results]),
                    'final_infection': np.mean([r['final_infection'] for r in ppo_results]),
                    'duration': np.mean([r['duration'] for r in ppo_results]),
                    'cumulative_infection': np.mean([r['cumulative_infection'] for r in ppo_results]),
                    'deleted_count': np.mean([r['deleted_count'] for r in ppo_results])
                }
                results['PPO'] = avg_ppo
                
                print(f"\nPPO平均结果:")
                print(f"  峰值感染数: {avg_ppo['peak_infection']:.1f}")
                print(f"  最终感染数: {avg_ppo['final_infection']:.1f}")
                print(f"  累积感染数: {avg_ppo['cumulative_infection']:.1f}")
                print(f"  删除节点数: {avg_ppo['deleted_count']:.1f}")
        
        except Exception as e:
            print(f"PPO测试失败: {e}")
            print("PPO可能需要重新训练以适应1000节点网络")
    
    except Exception as e:
        print(f"创建PPO环境失败: {e}")
    
    # 输出最终对比
    print(f"\n=== 1000节点网络最终对比 ===")
    print("| 方法 | 峰值感染 | 最终感染 | 累积感染 | 删除节点 |")
    print("|------|----------|----------|----------|----------|")
    
    for method_name, result in results.items():
        print(f"| {method_name:<8} | {result['peak_infection']:<8.1f} | "
              f"{result['final_infection']:<8.1f} | {result['cumulative_infection']:<8.1f} | "
              f"{result['deleted_count']:<8.1f} |")
    
    return results

if __name__ == "__main__":
    results = test_1000_nodes_convergence()
