"""
分析PPO的具体行为
理解为什么PPO只删除1个节点
"""

import numpy as np
import networkx as nx
import torch
from epidemic_environment import EpidemicEnvironment
from ppo_agent import PPOAgent
from final_optimized_train import create_synthetic_networks, prepare_state_for_agent

def analyze_ppo_episode():
    """分析PPO的一个完整episode"""
    print("=== 分析PPO的episode行为 ===")
    
    # 创建环境（使用改进后的参数）
    disease_graph, rumor_graph = create_synthetic_networks(200, 'WS', k=8, p=0.3)
    
    env = EpidemicEnvironment(
        disease_graph=disease_graph,
        rumor_graph=rumor_graph,
        max_steps=60,
        beta_d=0.2, gamma_d=0.1, beta_r=0.3, mu_r=0.15,
        lambda_dr=0.3, lambda_rd=0.2,
        alpha=0.25, beta=0.75, gamma=0, delta=0.01
    )
    
    # 创建PPO agent（使用训练好的参数）
    agent = PPOAgent(
        node_feature_dim=7,
        hidden_dim=128,
        num_gnn_layers=4,
        lr_actor=3e-5,
        lr_critic=3e-5,
        device='cuda' if torch.cuda.is_available() else 'cpu'
    )
    
    print(f"环境: {len(disease_graph.nodes())} 节点")
    
    # 重置环境
    obs = env.reset()
    state = prepare_state_for_agent(env)
    
    print(f"初始感染者: {np.sum(env.disease_states == 1)}")
    print(f"初始谣言者: {np.sum(env.rumor_states == 1)}")
    
    # 运行一个完整episode
    step_count = 0
    total_reward = 0
    
    for step in range(60):  # 最多60步
        current_infected = np.sum(env.disease_states == 1)
        current_rumor = np.sum(env.rumor_states == 1)
        
        print(f"\n步骤 {step+1}:")
        print(f"  当前状态: 感染{current_infected}, 谣言{current_rumor}")
        
        # PPO选择动作
        action, log_prob, value = agent.select_action(state, deterministic=True)
        
        print(f"  PPO选择动作: {action}")
        print(f"  状态价值估计: {value:.4f}")
        
        # 执行动作
        next_obs, reward, done, info = env.step(action)
        next_state = prepare_state_for_agent(env)
        
        total_reward += reward
        step_count += 1
        
        print(f"  奖励: {reward:.4f}")
        print(f"  新感染数: {info['infected_count']}")
        print(f"  完成标志: {done}")
        
        if done:
            print(f"  终止原因分析:")
            print(f"    - 感染数为0: {info['infected_count'] == 0}")
            print(f"    - 达到最大步数: {env.current_step >= env.max_steps}")
            print(f"    - 图为空: {len(env.disease_graph.nodes()) == 0}")
            break
        
        state = next_state
    
    print(f"\nEpisode总结:")
    print(f"  总步数: {step_count}")
    print(f"  累积奖励: {total_reward:.4f}")
    print(f"  删除节点数: {len(env.deleted_nodes)}")
    print(f"  最终感染数: {np.sum(env.disease_states == 1)}")

def test_different_initial_conditions():
    """测试不同初始条件下的行为"""
    print("\n=== 测试不同初始条件 ===")
    
    disease_graph, rumor_graph = create_synthetic_networks(100, 'WS', k=6, p=0.3)
    
    # 测试不同的初始感染数
    initial_infected_ratios = [0.05, 0.1, 0.2, 0.3]  # 5%, 10%, 20%, 30%
    
    for ratio in initial_infected_ratios:
        print(f"\n初始感染率: {ratio*100:.0f}%")
        
        env = EpidemicEnvironment(
            disease_graph=disease_graph,
            rumor_graph=rumor_graph,
            max_steps=30,
            beta_d=0.2, gamma_d=0.1, beta_r=0.3, mu_r=0.15,
            lambda_dr=0.3, lambda_rd=0.2
        )
        
        # 重置并设置初始感染数
        env.reset()
        n_initial_infected = int(len(disease_graph.nodes()) * ratio)
        env.disease_states[:] = 2  # 全部恢复
        env.disease_states[:n_initial_infected] = 1  # 设置感染数
        
        print(f"  设置初始感染数: {n_initial_infected}")
        
        # 运行几步看看行为
        for step in range(5):
            current_infected = np.sum(env.disease_states == 1)
            
            # 随机选择动作
            action_mask = env.get_action_mask()
            available_actions = np.where(action_mask)[0]
            
            if len(available_actions) == 0:
                break
                
            action = np.random.choice(available_actions)
            next_obs, reward, done, info = env.step(action)
            
            print(f"    步骤{step+1}: {current_infected} -> {info['infected_count']}, 奖励{reward:.4f}, 完成{done}")
            
            if done:
                break

def analyze_reward_landscape():
    """分析奖励地形"""
    print("\n=== 分析奖励地形 ===")
    
    disease_graph, rumor_graph = create_synthetic_networks(50, 'WS', k=4, p=0.3)
    
    env = EpidemicEnvironment(
        disease_graph=disease_graph,
        rumor_graph=rumor_graph,
        max_steps=20,
        beta_d=0.2, gamma_d=0.1, beta_r=0.3, mu_r=0.15,
        lambda_dr=0.3, lambda_rd=0.2
    )
    
    env.reset()
    
    print("删除节点数 | 感染数 | 奖励")
    print("-" * 30)
    
    # 模拟不同删除数量的奖励
    for deleted_count in range(0, 11):
        env.deleted_nodes = set(range(deleted_count))
        
        # 设置一个固定的感染数
        env.disease_states[:] = 2
        env.disease_states[:20] = 1  # 20个感染者
        
        reward = env._calculate_reward()
        infected_count = np.sum(env.disease_states == 1)
        
        print(f"{deleted_count:10d} | {infected_count:6d} | {reward:6.4f}")

def main():
    """主分析函数"""
    print("开始分析PPO行为...")
    
    # 分析PPO的episode行为
    analyze_ppo_episode()
    
    # 测试不同初始条件
    test_different_initial_conditions()
    
    # 分析奖励地形
    analyze_reward_landscape()
    
    print("\n分析结论:")
    print("1. 检查PPO是否学会了有效的删除策略")
    print("2. 验证不同初始条件下的环境行为")
    print("3. 理解奖励函数的激励机制")

if __name__ == "__main__":
    main()
