"""
测试改进后的环境
验证奖励缩放和传播参数调整的效果
"""

import numpy as np
import networkx as nx
from epidemic_environment import EpidemicEnvironment
from final_optimized_train import create_synthetic_networks

def test_improved_environment():
    """测试改进后的环境"""
    print("=== 测试改进后的环境 ===")
    
    # 创建网络
    disease_graph, rumor_graph = create_synthetic_networks(200, 'WS', k=8, p=0.3)
    
    # 创建改进后的环境
    env = EpidemicEnvironment(
        disease_graph=disease_graph,
        rumor_graph=rumor_graph,
        max_steps=60,
        beta_d=0.2, gamma_d=0.1, beta_r=0.3, mu_r=0.15,  # 调整后的参数
        lambda_dr=0.3, lambda_rd=0.2,
        alpha=0.25, beta=0.75, gamma=0, delta=0.01
    )
    
    print(f"网络规模: {len(disease_graph.nodes())} 节点")
    
    # 重置环境
    obs = env.reset()
    print(f"初始感染者数量: {np.sum(env.disease_states == 1)}")
    print(f"初始谣言传播者数量: {np.sum(env.rumor_states == 1)}")
    
    # 模拟多步执行
    step_count = 0
    total_reward = 0
    infection_history = []
    reward_history = []
    
    for step in range(20):  # 最多20步
        # 获取当前状态
        current_infected = np.sum(env.disease_states == 1)
        infection_history.append(current_infected)
        
        print(f"\n步骤 {step+1}:")
        print(f"  感染者: {current_infected}")
        
        # 随机选择动作
        action_mask = env.get_action_mask()
        available_actions = np.where(action_mask)[0]
        
        if len(available_actions) == 0:
            print("  没有可用动作，结束")
            break
            
        action = np.random.choice(available_actions)
        
        # 执行动作
        next_obs, reward, done, info = env.step(action)
        total_reward += reward
        reward_history.append(reward)
        step_count += 1
        
        print(f"  删除节点: {action}")
        print(f"  奖励: {reward:.4f}")
        print(f"  感染数变化: {current_infected} -> {info['infected_count']}")
        print(f"  完成标志: {done}")
        
        if done:
            print(f"  终止原因: 感染数={info['infected_count']}, 步数={env.current_step}/{env.max_steps}")
            break
    
    print(f"\n总结:")
    print(f"  执行步数: {step_count}")
    print(f"  累积奖励: {total_reward:.4f}")
    print(f"  最终感染数: {np.sum(env.disease_states == 1)}")
    print(f"  删除节点数: {len(env.deleted_nodes)}")
    print(f"  感染数变化: {infection_history}")
    print(f"  奖励范围: [{min(reward_history):.4f}, {max(reward_history):.4f}]")

def compare_old_vs_new():
    """对比旧版本和新版本的奖励"""
    print("\n=== 对比旧版本和新版本奖励 ===")
    
    # 创建网络
    disease_graph, rumor_graph = create_synthetic_networks(100, 'WS', k=6, p=0.3)
    
    # 新版本环境
    env_new = EpidemicEnvironment(
        disease_graph=disease_graph,
        rumor_graph=rumor_graph,
        max_steps=20,
        beta_d=0.2, gamma_d=0.1, beta_r=0.3, mu_r=0.15,
        lambda_dr=0.3, lambda_rd=0.2
    )
    
    env_new.reset()
    
    # 测试不同感染数的奖励
    test_cases = [50, 30, 10, 5, 1, 0]
    
    print("感染数 | 旧奖励 | 新奖励 | 改进")
    print("-" * 40)
    
    for infected_count in test_cases:
        # 设置感染数
        env_new.disease_states[:] = 2  # 全部恢复
        env_new.disease_states[:infected_count] = 1  # 设置感染数
        env_new.deleted_nodes = set(range(5))  # 假设删除了5个节点
        
        # 旧奖励计算
        old_reward = -float(infected_count)
        
        # 新奖励计算
        new_reward = env_new._calculate_reward()
        
        improvement = "更平滑" if abs(new_reward) < abs(old_reward) else "相同"
        
        print(f"{infected_count:6d} | {old_reward:7.1f} | {new_reward:7.4f} | {improvement}")

def main():
    """主测试函数"""
    print("测试改进后的环境...")
    
    # 测试改进后的环境
    test_improved_environment()
    
    # 对比新旧奖励
    compare_old_vs_new()
    
    print("\n改进总结:")
    print("✅ 奖励缩放：从[-200, 0]缩放到[-1, 0.6]")
    print("✅ 传播速度：降低传播率，提高恢复率")
    print("✅ 奖励塑形：删除节点给予小额奖励")
    print("✅ 梯度友好：PPO更容易学习")

if __name__ == "__main__":
    main()
