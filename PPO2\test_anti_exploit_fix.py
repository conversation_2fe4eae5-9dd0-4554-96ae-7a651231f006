"""
测试防止PPO利用漏洞的修复
验证新的终止条件和奖励机制
"""

import numpy as np
import networkx as nx
from epidemic_environment import EpidemicEnvironment
from final_optimized_train import create_synthetic_networks

def test_anti_exploit_termination():
    """测试防止利用漏洞的终止条件"""
    print("=== 测试防止利用漏洞的终止条件 ===")
    
    # 创建网络
    disease_graph, rumor_graph = create_synthetic_networks(100, 'WS', k=6, p=0.3)
    
    # 创建环境
    env = EpidemicEnvironment(
        disease_graph=disease_graph,
        rumor_graph=rumor_graph,
        max_steps=30,
        beta_d=0.2, gamma_d=0.1, beta_r=0.3, mu_r=0.15,
        lambda_dr=0.3, lambda_rd=0.2,
        alpha=0.25, beta=0.75, gamma=0, delta=0.01
    )
    
    print(f"网络规模: {len(disease_graph.nodes())} 节点")
    print(f"最小删除数: {max(1, int(0.05 * len(disease_graph.nodes())))} 个节点")
    print(f"最大删除数: {int(0.3 * len(disease_graph.nodes()))} 个节点")
    print(f"过度删除阈值: {int(0.8 * len(disease_graph.nodes()))} 个节点")
    
    # 重置环境
    obs = env.reset()
    print(f"初始感染者数量: {np.sum(env.disease_states == 1)}")
    
    # 测试场景1：正常删除
    print(f"\n=== 场景1：正常删除（10个节点） ===")
    env.reset()
    total_reward = 0
    
    for step in range(10):
        action_mask = env.get_action_mask()
        available_actions = np.where(action_mask)[0]
        
        if len(available_actions) == 0:
            print("  没有可用动作")
            break
            
        action = np.random.choice(available_actions)
        next_obs, reward, done, info = env.step(action)
        total_reward += reward
        
        print(f"  步骤{step+1}: 删除{action}, 感染{info['infected_count']}, 奖励{reward:.4f}, 完成{done}")
        
        if done:
            break
    
    print(f"  场景1总奖励: {total_reward:.4f}")
    
    # 测试场景2：尝试删除过多节点
    print(f"\n=== 场景2：尝试删除过多节点（50个节点） ===")
    env.reset()
    total_reward = 0
    
    for step in range(50):
        action_mask = env.get_action_mask()
        available_actions = np.where(action_mask)[0]
        
        if len(available_actions) == 0:
            print("  没有可用动作")
            break
            
        action = np.random.choice(available_actions)
        next_obs, reward, done, info = env.step(action)
        total_reward += reward
        
        if step % 10 == 0 or done:
            deleted_ratio = len(env.deleted_nodes) / env.n_nodes
            print(f"  步骤{step+1}: 删除{len(env.deleted_nodes)}个({deleted_ratio:.1%}), 感染{info['infected_count']}, 奖励{reward:.4f}, 完成{done}")
        
        if done:
            print(f"  终止原因: 删除{len(env.deleted_nodes)}个节点({deleted_ratio:.1%})")
            break
    
    print(f"  场景2总奖励: {total_reward:.4f}")
    
    # 测试场景3：达到最大步数
    print(f"\n=== 场景3：达到最大步数 ===")
    env.reset()
    total_reward = 0
    
    for step in range(30):  # 最大步数
        action_mask = env.get_action_mask()
        available_actions = np.where(action_mask)[0]
        
        if len(available_actions) == 0:
            print("  没有可用动作")
            break
            
        action = np.random.choice(available_actions)
        next_obs, reward, done, info = env.step(action)
        total_reward += reward
        
        if step >= 25 or done:  # 只显示最后几步
            print(f"  步骤{step+1}: 删除{len(env.deleted_nodes)}个, 感染{info['infected_count']}, 奖励{reward:.4f}, 完成{done}")
        
        if done:
            print(f"  终止原因: 步数{env.current_step}/{env.max_steps}")
            break
    
    print(f"  场景3总奖励: {total_reward:.4f}")

def test_reward_mechanism():
    """测试新的奖励机制"""
    print(f"\n=== 测试新的奖励机制 ===")
    
    # 创建网络
    disease_graph, rumor_graph = create_synthetic_networks(100, 'WS', k=6, p=0.3)
    
    # 创建环境
    env = EpidemicEnvironment(
        disease_graph=disease_graph,
        rumor_graph=rumor_graph,
        max_steps=30,
        beta_d=0.2, gamma_d=0.1, beta_r=0.3, mu_r=0.15,
        lambda_dr=0.3, lambda_rd=0.2
    )
    
    # 测试不同删除比例的奖励
    test_ratios = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8]
    
    for ratio in test_ratios:
        env.reset()
        
        # 模拟删除指定比例的节点
        target_deletions = int(ratio * env.n_nodes)
        env.deleted_nodes = set(range(target_deletions))  # 直接设置删除的节点
        
        # 计算奖励
        reward = env._calculate_reward()
        
        print(f"  删除{target_deletions}个节点({ratio:.0%}): 奖励 = {reward:.4f}")

def main():
    """主测试函数"""
    print("测试防止PPO利用漏洞的修复...")
    
    # 测试防止利用漏洞的终止条件
    test_anti_exploit_termination()
    
    # 测试新的奖励机制
    test_reward_mechanism()
    
    print("\n修复总结:")
    print("✅ 防止删除过多节点：超过80%强制终止")
    print("✅ 合理的早期终止：5%-30%删除范围")
    print("✅ 奖励惩罚机制：删除过多给予负奖励")
    print("✅ 防止PPO利用图为空的漏洞")

if __name__ == "__main__":
    main()
